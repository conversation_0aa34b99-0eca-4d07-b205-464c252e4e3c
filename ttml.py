import os
import shutil
import re
import time
import logging
from xml.dom.minidom import parse

# Recursively extract all text content from XML node and its children
def extract_text(node):
    text = ''
    if node.nodeType == node.TEXT_NODE and node.data.strip():
        text = node.data.strip() + '\n'
    for child in node.childNodes:
        text += extract_text(child)
    return text

# Normalize whitespace - remove all spaces for Chinese text, normalize for others
def process_text(line):
    if re.search(r'[\u4e00-\u9fa5]', line):
        return re.sub(r'\s+', '', line)
    return re.sub(r'\s+', ' ', line.strip())

# Check if file is ready for processing by verifying stable file size
def is_file_ready(path, wait=1.0):
    size1 = os.path.getsize(path)
    time.sleep(wait)
    return size1 == os.path.getsize(path)

# Convert TTML subtitle file to plain text and archive the original
def handle_ttml(path, watch_folder, original_folder, sanitize_and_trim_filename):
    lock = path + '.processing'
    filename = os.path.basename(path)

    try:
        # Read file to get character count before processing
        with open(path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        char_count = len(content)
        logging.info(f"TTML conversion: Starting processing for {filename} (characters: {char_count:,})")

        os.rename(path, lock)
        logging.debug(f"TTML conversion: Created processing lock for {filename}")

        # Get first line to determine file type (XML/TTML vs plain text)
        first = content.split('\n')[0] if content else ''
        content_length = len(content)
        logging.debug(f"TTML conversion: Read file content, length: {content_length:,} characters")

        base_name = sanitize_and_trim_filename(os.path.splitext(filename)[0])
        out_txt = os.path.join(watch_folder, base_name + '.txt')

        if not first.lstrip().startswith('<'):
            logging.debug(f"TTML conversion: Processing as plain text file (no XML structure)")
            with open(out_txt, 'w', encoding='utf-8') as f:
                f.write(content)
            output_length = content_length
        else:
            logging.debug(f"TTML conversion: Processing as XML/TTML file")
            dom = parse(lock)
            raw_lines = extract_text(dom.documentElement).splitlines()
            lines = [process_text(l) for l in raw_lines if l.strip()]
            processed_content = ' '.join(lines)

            logging.debug(f"TTML conversion: Extracted {len(raw_lines)} raw lines, processed to {len(lines)} clean lines")

            with open(out_txt, 'w', encoding='utf-8') as f:
                f.write(processed_content)
            output_length = len(processed_content)

        output_filename = os.path.basename(out_txt)
        logging.info(f"TTML conversion: Created text file {output_filename} ({output_length:,} characters)")

        # Use sanitized base_name for consistent archive naming
        archive_filename = base_name + '.ttml'
        archive_path = os.path.join(original_folder, archive_filename)
        shutil.move(lock, archive_path)

        logging.info(f"TTML conversion: Archived original TTML file as {archive_filename}")
        logging.info(f"TTML conversion: Completed for {filename} → {output_filename} (text file ready for downstream processing)")

    except Exception as e:
        logging.error(f"TTML conversion: Error processing {filename}: {e}")
        if os.path.exists(lock):
            try:
                os.rename(lock, path)
                logging.debug(f"TTML conversion: Restored original file {filename} after error")
            except Exception as restore_error:
                logging.error(f"TTML conversion: Failed to restore file {filename}: {restore_error}")
