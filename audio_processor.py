import os
import time
import whisper
import subprocess
import shutil
import logging
import warnings
from queue import Queue
import threading

from utils import sanitize_and_trim_filename

# Suppress Whisper warnings to reduce console noise
warnings.filterwarnings("ignore", message="FP16 is not supported on CPU; using FP32 instead")
warnings.filterwarnings("ignore", category=FutureWarning, message=r"You are using `torch.load` with `weights_only=False`")

# Audio processing configuration
MODEL_SIZE = "large-v3-turbo"  # Whisper model for transcription
SORT_ORDER = False  # False = smallest first, True = largest first

# ARCHITECTURE NOTE: Audio pipeline runs completely independently
# No other pipelines can block audio processing - true parallelism
# TTML, text queues, and cleaning all run independently

# Global audio queue and processing state
audio_queue = Queue()
loaded_model = None

# Check if folder contains audio files for processing
def find_audio_files_in_folder(path):
    if not os.path.exists(path):
        return False
    return any(file.endswith(('.mp4', '.mp3', '.m4a')) for file in os.listdir(path))

# Determine which folder to process based on time and file availability (root folder has priority)
def update_folder_path(config):
    current_hour = time.localtime().tm_hour
    is_night_time = current_hour >= 23 or current_hour < 5
    night_folder = os.path.join(config['WATCH_FOLDER'], 'Night')

    if find_audio_files_in_folder(config['WATCH_FOLDER']):
        logging.info(f"Found audio files in root folder: {config['WATCH_FOLDER']}")
        return config['WATCH_FOLDER']
    elif is_night_time and find_audio_files_in_folder(night_folder):
        logging.info(f"Found audio files in night folder: {night_folder}")
        return night_folder
    else:
        return None

# Get audio files sorted by size for processing order
def get_audio_files_sorted_by_size(folder_path):
    if not os.path.exists(folder_path):
        return []
    audio_files = [
        file for file in os.listdir(folder_path) if file.endswith(('.mp4', '.mp3', '.m4a'))
    ]
    audio_files.sort(key=lambda f: os.path.getsize(os.path.join(folder_path, f)), reverse=SORT_ORDER)
    return audio_files

# Convert audio file to WAV format for Whisper processing
def convert_audio_to_wav(folder_path, audio_file):
    input_path = os.path.join(folder_path, audio_file)
    output_file = audio_file.rsplit('.', 1)[0] + '.wav'
    output_path = os.path.join(folder_path, output_file)

    try:
        subprocess.run(
            ['ffmpeg', '-y', '-i', input_path, '-ac', '1', '-ar', '16000', output_path],
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        return output_path
    except subprocess.CalledProcessError as e:
        logging.error(f"Error converting {audio_file}: {e}")
        return None

# Move processed files and cleanup
def move_files_to_done(audio_file_path, wav_file_path, process_time, done_folder_path, sanitized_filename=None):
    if wav_file_path and os.path.exists(wav_file_path):
        os.remove(wav_file_path)

    # Use sanitized filename for archive if provided, otherwise use original
    archive_filename = sanitized_filename if sanitized_filename else os.path.basename(audio_file_path)
    target_path = os.path.join(done_folder_path, archive_filename)
    if os.path.exists(target_path):
        os.remove(target_path)
    shutil.move(audio_file_path, target_path)
    logging.info(f"Audio processing completed in {process_time:.2f} seconds. Archived as: {archive_filename}")

# Initialize Whisper model exactly as in W.py
def initialize_whisper_model():
    global loaded_model
    if loaded_model is None:
        logging.info("Loading Whisper model...")
        loaded_model = whisper.load_model(MODEL_SIZE)
        logging.info("Whisper model loaded successfully.")
    return loaded_model

# Check if any higher priority processes are active
def has_higher_priority_work(pretext_queue, extract_queue, premium_extract_queue, watch_folder):
    # COMPLETE INDEPENDENCE: No other processes block audio processing
    # All pipelines (TTML, Text, Audio, Cleaning) run independently
    # This function maintained for compatibility but always returns False

    # REMOVED: All blocking checks for true pipeline independence
    # - TTML files no longer block audio (independent TTML pipeline)
    # - Text queues no longer block audio (independent text pipeline)
    # - Audio pipeline runs completely independently

    return False  # Audio processing never blocked by other pipelines

# Scan for audio files and add to queue
def scan_audio_files(config):
    current_folder = update_folder_path(config)
    if current_folder:
        audio_files = get_audio_files_sorted_by_size(current_folder)
        for audio_file in audio_files:
            file_path = os.path.join(current_folder, audio_file)
            if file_path not in [item[0] for item in list(audio_queue.queue)]:
                audio_queue.put((file_path, current_folder))
                logging.info(f"Added audio file to queue: {audio_file}")

# Process single audio file through Whisper transcription
def process_audio_file(file_path, folder_path, config, done_folder_path):
    file_name = os.path.basename(file_path)

    # Sanitize and trim filename at entry point for consistent processing
    original_base_name = os.path.splitext(file_name)[0]
    extension = os.path.splitext(file_name)[1]
    sanitized_base_name = sanitize_and_trim_filename(original_base_name)
    sanitized_file_name = sanitized_base_name + extension

    # Convert to WAV
    wav_file_path = convert_audio_to_wav(folder_path, file_name)
    if not wav_file_path:
        logging.error(f"Failed to convert {file_name} to WAV format.")
        # Move problematic file to done folder using sanitized filename
        try:
            problematic_file = os.path.join(done_folder_path, sanitized_file_name)
            shutil.move(file_path, problematic_file)
            logging.info(f"Moved problematic file to: {problematic_file}")
        except Exception as e:
            logging.error(f"Error handling problematic file: {e}")
        return False
    
    try:
        start_time = time.time()
        logging.info(f"Starting transcription of: {file_name}")

        # Initialize Whisper model if needed
        model = initialize_whisper_model()

        # Transcribe audio exactly as in W.py
        logging.info("Processing transcription.")
        result = model.transcribe(wav_file_path)
        transcription_text = result["text"]
        logging.info("Transcription completed.")

        # Save transcription to text file using sanitized filename
        txt_filename = sanitized_base_name + '.txt'
        txt_file_path = os.path.join(config['WATCH_FOLDER'], txt_filename)

        with open(txt_file_path, 'w', encoding='utf-8') as f:
            f.write(transcription_text)

        # Move files and cleanup using sanitized filename for archive
        process_time = time.time() - start_time
        move_files_to_done(file_path, wav_file_path, process_time, done_folder_path, sanitized_file_name)

        logging.info(f"Audio transcription completed: {txt_filename}")
        return True
        
    except Exception as e:
        logging.error(f"Error during transcription of {file_name}: {e}")
        # Cleanup on error
        if wav_file_path and os.path.exists(wav_file_path):
            os.remove(wav_file_path)
        return False

# Main audio processing function - completely independent pipeline
def process_audio_queue(config, pretext_queue, extract_queue, premium_extract_queue, processing_lock, done_folder_path):
    while True:
        try:
            # No priority checks needed - audio pipeline runs independently
            # All pipelines operate in parallel without blocking
            
            # Scan for new audio files
            scan_audio_files(config)
            
            # Process audio files if queue is not empty
            if not audio_queue.empty():
                file_path, folder_path = audio_queue.get()
                
                # Double-check file still exists
                if not os.path.exists(file_path):
                    audio_queue.task_done()
                    continue
                
                # Acquire processing lock
                with processing_lock:
                    # Process the audio file (completely independent of all other pipelines)
                    success = process_audio_file(file_path, folder_path, config, done_folder_path)
                    audio_queue.task_done()
                    
                    if success:
                        logging.info("Audio file processed successfully.")
            else:
                time.sleep(10)  # Wait when no audio files to process
                
        except Exception as e:
            logging.error(f"Audio queue processing error: {str(e)}")
            if not audio_queue.empty():
                audio_queue.task_done()
            time.sleep(5)
