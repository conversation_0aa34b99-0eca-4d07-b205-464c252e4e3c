import os
import logging
from logging.handlers import RotatingFileHandler
import shutil
import sys
import threading
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from queue import Queue
from watchdog.observers import Observer

from pretext import Pretext<PERSON>and<PERSON>
from extract import ExtractHandler, PremiumExtractHandler
from ttml import handle_ttml, is_file_ready
from unlink import clean_dead_links, setup_wikilink_cleaner_logging, get_cleaning_stats
from utils import (
    read_prompt_file,
    sanitize_filename,
    sanitize_and_trim_filename,
    get_next_available_filename,
    safe_rename
)

load_dotenv()

class UTFStreamHandler(logging.StreamHandler):
    def emit(self, record):
        try:
            msg = self.format(record)
            stream = self.stream
            stream.buffer.write(msg.encode('utf-8'))
            stream.buffer.write(self.terminator.encode('utf-8'))
            self.flush()
        except Exception:
            self.handleError(record)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S',
    handlers=[
        RotatingFileHandler('script.log', maxBytes=1*1024*1024, backupCount=2, encoding='utf-8'),
        UTFStreamHandler(sys.stdout)
    ]
)

# Windows host directories accessed through devcontainer mounts
WATCH_FOLDER = '/mnt/desktop/Sync/Whisper'
ORIGINAL_FOLDER = os.path.join(WATCH_FOLDER, 'Archive', 'Raw')
PRETEXT_TARGET_FOLDER = WATCH_FOLDER
EXTRACT_FOLDER = os.path.join(WATCH_FOLDER, 'Archive', 'Extract')
PRETEXT_FOLDER = os.path.join(WATCH_FOLDER, 'Archive')
OBSIDIAN_SYNC_FOLDER = '/mnt/obsidian/OB Whisper'
DONE_FOLDER = '/mnt/desktop/YT1'
PREMIUM_WATCH_FOLDER = '/mnt/desktop'
LINK_BACKUP_FOLDER = os.path.join(WATCH_FOLDER, 'Archive', 'link_backup')

pretext_queue = Queue()
extract_queue = Queue()
premium_extract_queue = Queue()

text_processing_lock = threading.Lock()
audio_processing_lock = threading.Lock()

file_locks = {}
file_locks_mutex = threading.Lock()

# Wikilink cleaning variables
wikilink_cleaning_stats = {'last_run': None, 'cycle_count': 0}
shutdown_flag = threading.Event()

CONFIG = {
    'WATCH_FOLDER': WATCH_FOLDER,
    'ORIGINAL_FOLDER': ORIGINAL_FOLDER,
    'PRETEXT_TARGET_FOLDER': PRETEXT_TARGET_FOLDER,
    'EXTRACT_FOLDER': EXTRACT_FOLDER,
    'PRETEXT_FOLDER': PRETEXT_FOLDER,
    'OBSIDIAN_SYNC_FOLDER': OBSIDIAN_SYNC_FOLDER,
    'DONE_FOLDER': DONE_FOLDER,
    'LINK_BACKUP_FOLDER': LINK_BACKUP_FOLDER,
    'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
    'GPT_MODEL_PRETEXT': 'gpt-4.1-mini',
    'GPT_MODEL_EXTRACT_1': 'gpt-4.1-mini', # new models after 2025, gpt-4.5-preview , o1 ,  gpt-4o , o3 , gpt-4.1
    'GPT_MODEL_EXTRACT_2': 'o4-mini', # new models after 2025, o4-mini, gpt-4.1-mini
    'GPT_MODEL_EXTRACT_3': 'o1',
    'PREMIUM_WATCH_FOLDER': PREMIUM_WATCH_FOLDER,
    'PRETEXT_PROMPT': None,
    'EXTRACT_PROMPT': None
}

if not CONFIG['OPENAI_API_KEY']:
    raise ValueError("OpenAI API key not found. Please set OPENAI_API_KEY in your environment variables or .env file.")

CONFIG['PRETEXT_PROMPT'] = read_prompt_file('prompt_pretext.txt')
CONFIG['EXTRACT_PROMPT'] = read_prompt_file('prompt_extract.txt')

def acquire_file_lock(file_path):
    with file_locks_mutex:
        if file_path not in file_locks:
            file_locks[file_path] = threading.Lock()
        file_lock = file_locks[file_path]
    return file_lock.acquire(blocking=False)

def release_file_lock(file_path):
    with file_locks_mutex:
        if file_path in file_locks:
            file_locks[file_path].release()

def cleanup_file_lock(file_path):
    with file_locks_mutex:
        if file_path in file_locks:
            del file_locks[file_path]

def is_file_being_processed(file_path):
    with file_locks_mutex:
        if file_path in file_locks:
            return not file_locks[file_path].acquire(blocking=False)
        return False

# Global tracking for processed files to prevent duplicates
processed_files_global = set()
processed_files_lock = threading.Lock()

def sanitize_filename_if_needed(file_path):
    """
    Centralized filename sanitization to prevent duplicates.
    Returns (new_file_path, was_renamed) tuple.
    """
    filename = os.path.basename(file_path)
    base_name = os.path.splitext(filename)[0]

    # Only sanitize if base name > 60 characters
    if len(base_name) <= 60:
        return file_path, False

    # Generate sanitized filename
    sanitized_base = sanitize_and_trim_filename(base_name)
    new_filename = sanitized_base + '.txt'
    new_path = os.path.join(os.path.dirname(file_path), new_filename)

    # Check if sanitized file already exists
    if os.path.exists(new_path):
        logging.debug(f"Sanitized file already exists: {new_filename}")
        return new_path, True

    # Rename the file
    try:
        safe_rename(file_path, new_path)
        logging.info(f"Sanitized long filename: {base_name} -> {sanitized_base}")
        return new_path, True
    except Exception as e:
        logging.error(f"Error sanitizing filename {filename}: {str(e)}")
        return file_path, False

def add_to_pretext_queue_safely(file_path):
    """
    Safely add file to pretext queue with duplicate prevention.
    """
    with processed_files_lock:
        if file_path in processed_files_global:
            logging.debug(f"File already queued: {os.path.basename(file_path)}")
            return False

        # Check if file is already in queue
        queue_items = list(pretext_queue.queue)
        if file_path in queue_items:
            logging.debug(f"File already in queue: {os.path.basename(file_path)}")
            processed_files_global.add(file_path)
            return False

        # Add to queue and tracking
        pretext_queue.put(file_path)
        processed_files_global.add(file_path)
        logging.debug(f"Added to pretext queue: {os.path.basename(file_path)}")
        return True

pretext_handler = PretextHandler(CONFIG, pretext_queue, sanitize_and_trim_filename, safe_rename)
extract_handler = ExtractHandler(CONFIG, extract_queue)
premium_extract_handler = PremiumExtractHandler(CONFIG, premium_extract_queue)

def process_pretext_queue():
    from utils import OpenAIPermanentFailure
    while True:
        try:
            file_path = pretext_queue.get()
            filename = os.path.basename(file_path)
            logging.debug(f"Pretext queue status: {pretext_queue.qsize()} files pending")

            if acquire_file_lock(file_path):
                try:
                    with text_processing_lock:
                        try:
                            pretext_handler.process_pretext(file_path, get_next_available_filename)
                            if hasattr(pretext_handler, 'processed_files') and file_path in pretext_handler.processed_files:
                                pretext_handler.processed_files.discard(file_path)
                            pretext_queue.task_done()
                        except OpenAIPermanentFailure as e:
                            logging.error(f"Resilient Queue: OpenAI API permanent failure for file {e.file_path} (model: {e.model}): {e.reason}")
                            pretext_queue.put(file_path)  # Requeue to end
                            pretext_queue.task_done()
                        except Exception as e:
                            logging.error(f"Pretext queue error: {str(e)}")
                            pretext_queue.task_done()
                finally:
                    release_file_lock(file_path)
                    cleanup_file_lock(file_path)
            else:
                logging.debug(f"Text Pipeline: File {filename} is locked, requeuing for pretext processing")
                pretext_queue.put(file_path)
                pretext_queue.task_done()
                time.sleep(1)
        except Exception as e:
            logging.error(f"Pretext queue error (outer): {str(e)}")
            pretext_queue.task_done()
            if 'file_path' in locals():
                release_file_lock(file_path)
                cleanup_file_lock(file_path)
        time.sleep(0.5)

def process_extract_queue():
    from utils import OpenAIPermanentFailure
    while True:
        try:
            file_path = extract_queue.get()
            filename = os.path.basename(file_path)
            logging.debug(f"Extract queue status: {extract_queue.qsize()} files pending")

            if acquire_file_lock(file_path):
                try:
                    with text_processing_lock:
                        try:
                            extract_handler.process_extract(file_path, get_next_available_filename)
                            if hasattr(extract_handler, 'processed_files') and file_path in extract_handler.processed_files:
                                extract_handler.processed_files.discard(file_path)
                            extract_queue.task_done()
                        except OpenAIPermanentFailure as e:
                            logging.error(f"Resilient Queue: OpenAI API permanent failure for file {e.file_path} (model: {e.model}): {e.reason}")
                            extract_queue.put(file_path)  # Requeue to end
                            extract_queue.task_done()
                        except Exception as e:
                            logging.error(f"Extract queue error: {str(e)}")
                            extract_queue.task_done()
                finally:
                    release_file_lock(file_path)
                    cleanup_file_lock(file_path)
            else:
                logging.debug(f"Text Pipeline: File {filename} is locked, requeuing for extract processing")
                extract_queue.put(file_path)
                extract_queue.task_done()
                time.sleep(1)
        except Exception as e:
            logging.error(f"Extract queue error (outer): {str(e)}")
            extract_queue.task_done()
            if 'file_path' in locals():
                release_file_lock(file_path)
                cleanup_file_lock(file_path)
        time.sleep(0.5)

def process_premium_extract_queue():
    from utils import OpenAIPermanentFailure
    while True:
        try:
            file_path = premium_extract_queue.get()
            filename = os.path.basename(file_path)
            logging.debug(f"Premium extract queue status: {premium_extract_queue.qsize()} files pending")

            if acquire_file_lock(file_path):
                try:
                    with text_processing_lock:
                        try:
                            premium_extract_handler.process_premium_extract(file_path, get_next_available_filename)
                            if hasattr(premium_extract_handler, 'processed_files') and file_path in premium_extract_handler.processed_files:
                                premium_extract_handler.processed_files.discard(file_path)
                            premium_extract_queue.task_done()
                        except OpenAIPermanentFailure as e:
                            logging.error(f"Resilient Queue: OpenAI API permanent failure for file {e.file_path} (model: {e.model}): {e.reason}")
                            premium_extract_queue.put(file_path)  # Requeue to end
                            premium_extract_queue.task_done()
                        except Exception as e:
                            logging.error(f"Premium extract queue error: {str(e)}")
                            premium_extract_queue.task_done()
                finally:
                    release_file_lock(file_path)
                    cleanup_file_lock(file_path)
            else:
                logging.debug(f"Text Pipeline: File {filename} is locked, requeuing for premium extract processing")
                premium_extract_queue.put(file_path)
                premium_extract_queue.task_done()
                time.sleep(1)
        except Exception as e:
            logging.error(f"Premium extract queue error (outer): {str(e)}")
            premium_extract_queue.task_done()
            if 'file_path' in locals():
                release_file_lock(file_path)
                cleanup_file_lock(file_path)
        time.sleep(0.5)

def scan_existing_files():
    logging.info("Scanning for existing files to process...")

    for filename in os.listdir(WATCH_FOLDER):
        if not filename.lower().endswith('.txt'):
            continue
        file_path = os.path.join(WATCH_FOLDER, filename)
        if len(os.path.splitext(filename)[0]) > 60:
            # FIX: Pass only base name to sanitize_and_trim_filename
            base_name = os.path.splitext(filename)[0]
            sanitized_base = sanitize_and_trim_filename(base_name)
            new_name = sanitized_base + '.txt'
            new_path = os.path.join(WATCH_FOLDER, new_name)
            try:
                if not os.path.exists(new_path):
                    safe_rename(file_path, new_path)
                    file_path = new_path
                    logging.debug(f"Renamed long filename: {filename} -> {new_name}")
            except Exception as e:
                logging.error(f"Error renaming file: {str(e)}")
                continue

        if (filename.lower().endswith('.txt') and
            not filename.lower().endswith('_p.txt')):
            pretext_queue.put(file_path)
            logging.debug(f"Added existing file to pretext queue: {filename}")

    for filename in os.listdir(WATCH_FOLDER):
        if filename.lower().endswith('_p.txt'):
            file_path = os.path.join(WATCH_FOLDER, filename)
            extract_queue.put(file_path)
            logging.debug(f"Added existing pretext file to extract queue: {filename}")

    for filename in os.listdir(PREMIUM_WATCH_FOLDER):
        if filename.lower().endswith('_p.txt'):
            file_path = os.path.join(PREMIUM_WATCH_FOLDER, filename)
            premium_extract_queue.put(file_path)
            logging.debug(f"Added existing pretext file to premium extract queue: {filename}")

    logging.info(f"Initial scan complete. Queued: {pretext_queue.qsize()} pretext, {extract_queue.qsize()} extract, {premium_extract_queue.qsize()} premium extract files")

def periodic_file_scanner():
    processed_files = set()
    scan_count = 0

    while True:
        try:
            time.sleep(30)
            scan_count += 1
            current_files = set()
            new_files_found = 0


            for filename in os.listdir(WATCH_FOLDER):
                if (filename.lower().endswith('.txt') and
                    not filename.lower().endswith('_p.txt')):
                    file_path = os.path.join(WATCH_FOLDER, filename)
                    current_files.add(file_path)
                    if file_path in processed_files:
                        continue
                    queue_items = list(pretext_queue.queue)
                    if file_path not in queue_items:
                        pretext_queue.put(file_path)
                        processed_files.add(file_path)
                        new_files_found += 1
                    else:
                        processed_files.add(file_path)

            processed_files = processed_files.intersection(current_files)

        except Exception as e:
            logging.error(f"Periodic scanner error: {str(e)}")
            time.sleep(60)

def process_audio_pipeline():
    from audio_processor import process_audio_queue
    process_audio_queue(CONFIG, pretext_queue, extract_queue, premium_extract_queue, audio_processing_lock, DONE_FOLDER)

def process_ttml_pipeline():
    """Independent TTML processing pipeline - highest responsiveness for subtitle files."""
    while not shutdown_flag.is_set():
        try:
            # Scan for TTML files
            ttml_files = []
            if os.path.exists(WATCH_FOLDER):
                ttml_files = [fn for fn in os.listdir(WATCH_FOLDER) if fn.lower().endswith('.ttml')]

            if ttml_files:
                logging.debug(f"TTML Pipeline: Found {len(ttml_files)} TTML files for processing")

                for fn in ttml_files:
                    if shutdown_flag.is_set():
                        return

                    src = os.path.join(WATCH_FOLDER, fn)

                    # Check if file is ready for processing
                    if not is_file_ready(src):
                        logging.debug(f"TTML Pipeline: File {fn} is not ready (still being written), skipping")
                        continue

                    # Try to acquire file lock (non-blocking)
                    if acquire_file_lock(src):
                        try:
                            # Log file info
                            try:
                                with open(src, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                    char_count = len(content)
                                    logging.debug(f"TTML Pipeline: Processing {fn} ({char_count:,} characters)")
                            except Exception:
                                try:
                                    file_size = os.path.getsize(src)
                                    logging.debug(f"TTML Pipeline: Processing {fn} ({file_size:,} bytes)")
                                except Exception:
                                    logging.debug(f"TTML Pipeline: Processing {fn}")

                            # Process the TTML file
                            handle_ttml(src, WATCH_FOLDER, ORIGINAL_FOLDER, sanitize_and_trim_filename)

                        except Exception as e:
                            logging.error(f"TTML Pipeline: Error processing {fn}: {str(e)}")
                        finally:
                            release_file_lock(src)
                            cleanup_file_lock(src)
                    else:
                        logging.debug(f"TTML Pipeline: File {fn} is locked, skipping")

            # Sleep briefly before next scan (responsive to new files)
            time.sleep(2)

        except Exception as e:
            logging.error(f"TTML Pipeline: Error during scan: {str(e)}")
            time.sleep(5)

def process_wikilink_cleaning():
    """Process wikilink cleaning with hourly schedule - completely independent."""
    global wikilink_cleaning_stats

    # Wait 5 minutes after startup before first cleaning
    for _ in range(300):  # 5 minutes = 300 seconds
        if shutdown_flag.wait(1):  # Check every second for shutdown
            return

    while not shutdown_flag.is_set():
        try:
            # Perform cleaning (no dependency checks - complete independence)
            wikilink_cleaning_stats['cycle_count'] += 1
            cycle_start = time.time()

            logging.info(f"WikilinkCleaner: Starting cleaning cycle #{wikilink_cleaning_stats['cycle_count']}")

            # Prepare file lock functions for wikilink cleaner
            file_lock_functions = {
                'acquire': acquire_file_lock,
                'release': release_file_lock,
                'cleanup': cleanup_file_lock
            }

            stats = clean_dead_links(
                target_dir=CONFIG['OBSIDIAN_SYNC_FOLDER'],
                backup_dir=CONFIG['LINK_BACKUP_FOLDER'],
                create_backup=True,
                dry_run=False,
                max_files=50,
                file_lock_functions=file_lock_functions
            )

            cycle_duration = time.time() - cycle_start
            wikilink_cleaning_stats['last_run'] = datetime.now()

            # Log summary every 10 cycles or if files were modified
            if wikilink_cleaning_stats['cycle_count'] % 10 == 0 or stats['files_modified'] > 0:
                next_run = datetime.now() + timedelta(hours=1)
                logging.info(f"WikilinkCleaner: Cycle #{wikilink_cleaning_stats['cycle_count']} completed in {cycle_duration:.1f}s - "
                           f"Files: {stats['files_processed']}, Links removed: {stats['broken_links_removed']}, "
                           f"Files modified: {stats['files_modified']}, Next run: {next_run.strftime('%H:%M')}")

        except Exception as e:
            logging.error(f"WikilinkCleaner: Error during cleaning cycle: {str(e)}")

        # Wait 1 hour (3600 seconds) before next cleaning cycle
        # Use 30-second intervals to allow for responsive shutdown
        for _ in range(120):  # 120 * 30 = 3600 seconds = 1 hour
            if shutdown_flag.wait(30):
                return

def main():
    logging.info("Starting 4 independent parallel pipeline system: TTML + Text + Audio + WikilinkCleaner")

    # Set up wikilink cleaner logging
    setup_wikilink_cleaner_logging(logging.getLogger())

    os.makedirs(ORIGINAL_FOLDER, exist_ok=True)
    os.makedirs(DONE_FOLDER, exist_ok=True)
    os.makedirs(CONFIG['LINK_BACKUP_FOLDER'], exist_ok=True)

    # Start all 4 independent pipelines
    threading.Thread(target=process_ttml_pipeline, daemon=True, name="TTMLPipeline").start()
    threading.Thread(target=process_pretext_queue, daemon=True, name="TextPipeline-Pretext").start()
    threading.Thread(target=process_extract_queue, daemon=True, name="TextPipeline-Extract").start()
    threading.Thread(target=process_premium_extract_queue, daemon=True, name="TextPipeline-PremiumExtract").start()
    threading.Thread(target=process_audio_pipeline, daemon=True, name="AudioPipeline").start()
    threading.Thread(target=periodic_file_scanner, daemon=True, name="PeriodicScanner").start()
    threading.Thread(target=process_wikilink_cleaning, daemon=True, name="WikilinkCleaner").start()

    scan_existing_files()

    observer = Observer()
    observer.schedule(pretext_handler, WATCH_FOLDER, recursive=False)
    observer.schedule(extract_handler, WATCH_FOLDER, recursive=False)
    observer.schedule(premium_extract_handler, PREMIUM_WATCH_FOLDER, recursive=False)
    observer.start()

 #   logging.info("Quad-pipeline system started successfully")
    logging.info("Pipeline Architecture: 4 Completely Independent Parallel Systems")
    logging.info("TTML Pipeline: Independent subtitle file processing")
    logging.info("Text Pipeline: Pretext → Extract/Premium Extract (OpenAI API)")
    logging.info("Audio Pipeline: Independent local Whisper processing")
    logging.info("Cleaning Pipeline: WikilinkCleaner every hour")
    logging.info("Note: All pipelines run independently - no blocking between pipelines")

    try:
        # Main coordination loop - now only handles system monitoring
        while True:
            # Log queue status periodically (for monitoring purposes only)
            total_queues = pretext_queue.qsize() + extract_queue.qsize() + premium_extract_queue.qsize()
            if total_queues > 0:
                logging.debug(f"System Status - Text queues: Pretext: {pretext_queue.qsize()}, "
                            f"Extract: {extract_queue.qsize()}, Premium Extract: {premium_extract_queue.qsize()}")

            # Sleep longer since main loop no longer processes files
            time.sleep(10)
    except KeyboardInterrupt:
        shutdown_flag.set()  # Signal all threads to shutdown
        observer.stop()
        observer.join()
        logging.info("4-pipeline independent parallel system stopped")

if __name__ == "__main__":
    main()
