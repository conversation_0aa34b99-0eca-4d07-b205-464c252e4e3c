import os
import logging
import openai
import time
import re

# Filename processing utilities moved from P.py
def read_prompt_file(filename):
    """Read a prompt file from the script directory."""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        prompt_path = os.path.join(script_dir, filename)
        with open(prompt_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except Exception as e:
        logging.error(f"Error reading prompt file {filename}: {str(e)}")
        raise ValueError(f"Failed to load {filename}. Ensure the file exists in the script directory.")

def sanitize_filename(name):
    """Sanitize a filename by replacing problematic characters with spaces."""
    for ch in ['#', '[', ']', '`', '/', '\\', '?', '*', '<', '>', '|', '：', ':', '｜']:
        name = name.replace(ch, '・')
    return name

def sanitize_and_trim_filename(base_name, max_length=60):
    """Sanitize and trim a base filename (without extension) for consistent processing."""
    sanitized_name = sanitize_filename(base_name)
    try:
        if len(sanitized_name) > max_length:
            return sanitized_name[:max_length]
        return sanitized_name
    except Exception as e:
        logging.error(f"Error trimming base name '{base_name}': {str(e)}")
        return sanitized_name

def get_next_available_filename(base_path, base_name, suffix='_e'):
    """Generate the next available filename with optional suffix and counter."""
    initial_path = os.path.join(base_path, f"{base_name}{suffix}.txt")
    if not os.path.exists(initial_path):
        return initial_path
    counter = 1
    while True:
        numbered_path = os.path.join(base_path, f"{base_name}{suffix}_{counter}.txt")
        if not os.path.exists(numbered_path):
            return numbered_path
        counter += 1

def safe_rename(old_path, new_path):
    """Safely rename a file, avoiding overwrites."""
    try:
        if not os.path.exists(new_path):
            os.rename(old_path, new_path)
            return new_path
        return old_path
    except Exception as e:
        logging.error(f"Rename failed {old_path} -> {new_path}: {e}")
        return old_path

def get_model_display_name(api_model_name):
    if not api_model_name:
        return "Unknown Model"

    display_name = api_model_name
    if display_name.startswith('gpt-'):
        display_name = display_name.replace('gpt-', 'GPT-')
        if display_name.endswith('-turbo'):
            display_name = display_name.replace('-turbo', '-Turbo')
        elif display_name.endswith('-mini'):
            display_name = display_name.replace('-mini', '-Mini')
    return display_name

def chunk_text(text, chunk_size=2000, overlap=20):
    text_length = len(text)
    n_chunks = (text_length + chunk_size - 1) // chunk_size
    if n_chunks <= 1:
        return [text]
    optimal_chunk_size = text_length // n_chunks
    if optimal_chunk_size < (chunk_size * 0.5):
        n_chunks = max(1, (text_length + chunk_size * 0.5 - 1) // (chunk_size * 0.5))
        optimal_chunk_size = text_length // n_chunks
    chunks = []
    start = 0
    for i in range(n_chunks):
        if i == n_chunks - 1:
            chunks.append(text[start:])
            break
        end = start + optimal_chunk_size
        if i < n_chunks - 1:
            end += overlap
        chunks.append(text[start:end])
        start = end - overlap
    return chunks

class OpenAIPermanentFailure(Exception):
    """Raised when OpenAI API fails after all retries."""
    def __init__(self, message, model=None, file_path=None, reason=None):
        super().__init__(message)
        self.model = model
        self.file_path = file_path
        self.reason = reason

def call_openai_with_retry(client, model, messages, config=None, max_retries=2, file_path=None):
    wait_time = 10
    logging.debug(f"OpenAI API call: Starting request to {model}")
    for attempt in range(max_retries):
        try:
            pretext_model = config.get('GPT_MODEL_PRETEXT', 'gpt-4.1-mini') if config else 'gpt-4.1-mini'
            params = {
                "model": model,
                "messages": messages,
                "temperature": 0.2 if model == 'gpt-4.1-mini' else 1.0,
                "timeout": 30
            }
            logging.debug(f"OpenAI API call: Attempt {attempt + 1}/{max_retries} to {model}")
            response = client.chat.completions.create(**params)
            response_content = response.choices[0].message.content
            logging.debug(f"OpenAI API call: Success for {model}, response length: {len(response_content):,} characters")
            return response_content
        except Exception as e:
            if attempt == max_retries - 1:
                logging.error(f"OpenAI API call: Final attempt failed for {model}: {str(e)} | File: {file_path}")
                raise OpenAIPermanentFailure(
                    f"OpenAI API failed after {max_retries} attempts for model {model} on file {file_path}: {str(e)}",
                    model=model,
                    file_path=file_path,
                    reason=str(e)
                )
            wait_time = min(wait_time * 2, 15)
            logging.warning(f"OpenAI API call: Attempt {attempt + 1} failed for {model}: {str(e)}, retrying in {wait_time}s")
            time.sleep(wait_time)

def process_text_with_openai(client, model, system_prompt, text, config=None, file_path=None):
    response = call_openai_with_retry(client, model, [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": text}
    ], config, file_path=file_path)
    return response

def intelligent_merge_chunks(chunks, window=40, min_len=4):
    if not chunks:
        return ''
    if len(chunks) == 1:
        return chunks[0]

    def longest_common_substring(a, b):
        max_len = 0
        start_a = start_b = 0
        dp = [[0] * (len(b) + 1) for _ in range(len(a) + 1)]
        for i in range(1, len(a) + 1):
            for j in range(1, len(b) + 1):
                if a[i - 1] == b[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                    if dp[i][j] > max_len:
                        max_len = dp[i][j]
                        start_a = i - max_len
                        start_b = j - max_len
        return start_a, start_b, max_len

    merged = chunks[0]
    for i in range(1, len(chunks)):
        prev = merged[-window:] if len(merged) > window else merged
        curr = chunks[i][:window] if len(chunks[i]) > window else chunks[i]
        start_a, start_b, lcs_len = longest_common_substring(prev, curr)
        if lcs_len >= min_len:
            merged_pos = len(merged) - len(prev) + start_a
            curr_pos = start_b + lcs_len
            merged = (
                merged[:merged_pos]
                + '==' + prev[start_a:start_a + lcs_len] + '=='
                + chunks[i][curr_pos:]
            )
        else:
            merged += chunks[i]
    return merged

def find_most_recent_md_by_prefix(folder, prefix):
    pattern = re.compile(rf'^{re.escape(prefix)}_(\d{{6}})\.md$', re.IGNORECASE)
    most_recent = None
    most_recent_date = None
    for fname in os.listdir(folder):
        match = pattern.match(fname)
        if match:
            datecode = match.group(1)
            if most_recent_date is None or datecode > most_recent_date:
                most_recent = fname
                most_recent_date = datecode
    if most_recent:
        return os.path.join(folder, most_recent), most_recent_date
    return None, None
