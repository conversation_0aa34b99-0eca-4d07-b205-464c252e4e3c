import os
import logging
import openai
import shutil
import time
from datetime import datetime
from watchdog.events import FileSystemEventHandler

from utils import (
    get_model_display_name, 
    chunk_text, 
    process_text_with_openai, 
    intelligent_merge_chunks
)

class PretextHandler(FileSystemEventHandler):
    def __init__(self, config, queue, sanitize_and_trim_filename, safe_rename):
        self.config = config
        self.queue = queue
        self.sanitize_and_trim_filename = sanitize_and_trim_filename
        self.safe_rename = safe_rename
        self.processed_files = set()

    def on_created(self, event):
        if event.is_directory:
            return
        try:
            file_path = event.src_path
            if (os.path.dirname(file_path) == self.config['WATCH_FOLDER'] and
                file_path.lower().endswith('.txt') and
                not os.path.basename(file_path).lower().endswith('_p.txt')):

                filename = os.path.basename(file_path)

                import time
                time.sleep(0.5)

                if not os.path.exists(file_path):
                    logging.warning(f"File disappeared before processing: {filename}")
                    return

                try:
                    filename.encode('ascii')
                except UnicodeEncodeError:
                    logging.debug(f"Processing non-ASCII filename: {filename}")

                base_name = self.sanitize_and_trim_filename(os.path.splitext(filename)[0])
                new_filename = base_name + '.txt'
                new_path = os.path.join(self.config['WATCH_FOLDER'], new_filename)
                try:
                    if not os.path.exists(new_path):
                        self.safe_rename(file_path, new_path)
                        file_path = new_path
                        logging.debug(f"Renamed long filename: {filename} -> {new_filename}")
                except Exception as e:
                    logging.error(f"Error renaming file {filename}: {str(e)}")

                if file_path not in self.processed_files:
                    self.queue.put(file_path)
                    self.processed_files.add(file_path)
                else:
                    logging.debug(f"File already processed by pretext handler: {filename}")

        except Exception as e:
            logging.error(f"Error in PretextHandler.on_created: {str(e)}")

    def on_moved(self, event):
        if event.is_directory:
            return
        try:
            dest_path = event.dest_path
            if (os.path.dirname(dest_path) == self.config['WATCH_FOLDER'] and
                dest_path.lower().endswith('.txt') and
                not os.path.basename(dest_path).lower().endswith('_p.txt')):

                if dest_path not in self.processed_files:
                    self.queue.put(dest_path)
                    self.processed_files.add(dest_path)

        except Exception as e:
            logging.error(f"Error in PretextHandler.on_moved: {str(e)}")

    def process_pretext(self, file_path, get_next_available_filename):
        try:
            os.makedirs(self.config['ORIGINAL_FOLDER'], exist_ok=True)
            if not os.path.exists(file_path):
                return

            original_filename = os.path.basename(file_path)
            base_name = self.sanitize_and_trim_filename(os.path.splitext(original_filename)[0])
            # Use sanitized base_name for consistent archive naming
            archive_filename = base_name + '.txt'
            original_path = os.path.join(self.config['ORIGINAL_FOLDER'], archive_filename)

            # Read file to get character count
            content = None
            encoding_used = None
            for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        encoding_used = encoding
                        break
                except UnicodeDecodeError:
                    continue
            if content is None:
                raise ValueError(f"Unable to read file: {file_path}")

            char_count = len(content)
            logging.info(f"Pretext processing: Starting for {original_filename} (characters: {char_count:,})")

            logging.debug(f"File read successfully using {encoding_used} encoding, content length: {len(content):,} characters")

            client = openai.OpenAI(api_key=self.config['OPENAI_API_KEY'])
            chunks = chunk_text(content)
            logging.info(f"Pretext processing: Split into {len(chunks)} chunks for {original_filename}")

            all_results = []
            for i, chunk in enumerate(chunks, 1):
                logging.debug(f"Pretext API call {i}/{len(chunks)} for {original_filename} using {self.config['GPT_MODEL_PRETEXT']}")
                try:
                    chunk_result = process_text_with_openai(client, self.config['GPT_MODEL_PRETEXT'], self.config['PRETEXT_PROMPT'], chunk, self.config, file_path=file_path)
                except Exception as e:
                    logging.error(f"Pretext API call failed for chunk {i} of {original_filename}: {str(e)}")
                    raise  # propagate for queue handler to catch
                if chunk_result:
                    all_results.insert(0, chunk_result)
                    logging.debug(f"Pretext API call {i}/{len(chunks)} successful, response length: {len(chunk_result):,} characters")
                else:
                    raise ValueError(f"Empty response from OpenAI API for chunk {i}")
            all_results.reverse()
            pretext_result = intelligent_merge_chunks(all_results)
            if not pretext_result:
                raise ValueError("Empty combined response from OpenAI API")

            pretext_char_count = len(pretext_result)
            logging.info(f"Pretext processing: Completed for {original_filename} (output characters: {pretext_char_count:,})")

            pretext_target_path = os.path.join(self.config['PRETEXT_TARGET_FOLDER'], f"{base_name}_p.txt")
            with open(pretext_target_path, 'w', encoding='utf-8') as f:
                f.write(pretext_result)

            pretext_filename = os.path.basename(pretext_target_path)
            logging.info(f"Pretext processing: Created pretext file {pretext_filename}")

            # Create markdown file in Obsidian sync folder
            md_base_name = base_name
            os.makedirs(self.config['OBSIDIAN_SYNC_FOLDER'], exist_ok=True)
            datecode = datetime.now().strftime('%y%m%d')
            md_filename = f"{md_base_name}_{datecode}.md"
            md_path = os.path.join(self.config['OBSIDIAN_SYNC_FOLDER'], md_filename)
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(pretext_result)

            whisper_md_path = os.path.join(self.config['OBSIDIAN_SYNC_FOLDER'], 'Whisper.md')
            link_code = f"[[{md_base_name}_{datecode}]]\n"
            try:
                if os.path.exists(whisper_md_path):
                    with open(whisper_md_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    if lines:
                        # Find the first line that contains only "---" (with optional whitespace)
                        insert_index = None
                        for i, line in enumerate(lines):
                            if line.strip() == "---":
                                insert_index = i + 1  # Insert after the "---" line
                                break

                        if insert_index is not None:
                            # Check if there's already an empty line after the "---"
                            if insert_index < len(lines) and lines[insert_index].strip() == "":
                                # Empty line already exists, insert the link after it
                                lines.insert(insert_index + 1, link_code)
                            else:
                                # No empty line after "---", insert empty line first, then the link
                                lines.insert(insert_index, "\n")  # Insert empty line
                                lines.insert(insert_index + 1, link_code)  # Insert link after empty line
                        else:
                            # No "---" found, insert at line 1 (fallback to original behavior)
                            lines.insert(1, link_code)
                    else:
                        lines = [link_code]

                    with open(whisper_md_path, 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                else:
                    with open(whisper_md_path, 'w', encoding='utf-8') as f:
                        f.write(link_code)
            except Exception as e:
                logging.error(f"Error updating Whisper.md: {str(e)}")

            shutil.move(file_path, original_path)

        except Exception as e:
            logging.error(f"Error processing file: {str(e)}")
            if 'pretext_result' in locals():
                with open(pretext_target_path + ".error.txt", 'w', encoding='utf-8') as f:
                    f.write(f"Error: {str(e)}\nPartial response:\n{pretext_result}")
            raise  # propagate for queue handler to catch
