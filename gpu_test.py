#!/usr/bin/env python3
import os
import torch
from pynvml import (
    nvmlInit, nvmlShutdown,
    nvmlSystemGetDriverVersion, nvmlSystemGetNVMLVersion,
    nvmlDeviceGetCount, nvmlDeviceGetHandleByIndex,
    nvmlDeviceGetName, nvmlDeviceGetPciInfo,
    nvmlDeviceGetMemoryInfo, nvmlDeviceGetTemperature,
    nvmlDeviceGetFanSpeed, nvmlDeviceGetUtilizationRates,
    nvmlDeviceGetPowerUsage, nvmlDeviceGetPowerManagementLimit,
    NVMLError, NVMLError_NotSupported
)

def human_readable_bytes(num, suffix='B'):
    for unit in ['','K','M','G','T','P']:
        if abs(num) < 1024.0:
            return f"{num:3.1f}{unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f}Y{suffix}"

def main():
    print("=== NVML 信息 ===")
    nvmlInit()
    try:
        print(f"NVML 版本: {nvmlSystemGetNVMLVersion().decode()}")
        print(f"NVIDIA 驱动版本: {nvmlSystemGetDriverVersion().decode()}")
    except NVMLError as e:
        print(f"查询 NVML/驱动 版本失败: {e}")
    cuda_rt = os.environ.get("CUDA_VERSION", "未知")
    print(f"CUDA 运行时版本: {cuda_rt}\n")

    try:
        count = nvmlDeviceGetCount()
    except NVMLError as e:
        print(f"查询设备数量失败: {e}")
        nvmlShutdown()
        return

    print(f"检测到 {count} 个 CUDA 设备（NVML）")
    print("-" * 60)

    for i in range(count):
        try:
            h = nvmlDeviceGetHandleByIndex(i)
            name = nvmlDeviceGetName(h).decode()
            pci  = nvmlDeviceGetPciInfo(h).busId.decode()
            print(f"[NVML] 设备 {i}: {name} (PCI {pci})")
        except NVMLError as e:
            print(f"[NVML] 获取设备 {i} 信息失败: {e}")
            continue

        # 内存
        try:
            mem = nvmlDeviceGetMemoryInfo(h)
            print(f"  内存: 总 {human_readable_bytes(mem.total)}, 用 {human_readable_bytes(mem.used)}, 剩 {human_readable_bytes(mem.free)}")
        except NVMLError as e:
            print(f"  内存信息 不可用: {e}")

        # 温度
        try:
            temp = nvmlDeviceGetTemperature(h, 0)
            print(f"  温度: {temp} °C")
        except NVMLError as e:
            print(f"  温度 不可用: {e}")

        # 风扇速度
        try:
            fan = nvmlDeviceGetFanSpeed(h)
            print(f"  风扇速度: {fan} %")
        except NVMLError_NotSupported:
            print("  风扇速度: 不支持")
        except NVMLError as e:
            print(f"  风扇速度 查询失败: {e}")

        # 利用率
        try:
            util = nvmlDeviceGetUtilizationRates(h)
            print(f"  利用率: GPU {util.gpu}%  显存 {util.memory}%")
        except NVMLError as e:
            print(f"  利用率 不可用: {e}")

        # 功耗
        try:
            power = nvmlDeviceGetPowerUsage(h) / 1000.0
            print(f"  当前功耗: {power:.1f} W")
        except NVMLError_NotSupported:
            print("  当前功耗: 不支持")
        except NVMLError as e:
            print(f"  当前功耗 查询失败: {e}")

        # 功率限制
        try:
            limit = nvmlDeviceGetPowerManagementLimit(h) / 1000.0
            print(f"  功率限制: {limit:.1f} W")
        except NVMLError_NotSupported:
            print("  功率限制: 不支持")
        except NVMLError as e:
            print(f"  功率限制 查询失败: {e}")

        print("-" * 60)

    nvmlShutdown()

    print("\n=== PyTorch 信息 ===")
    gpu_cnt = torch.cuda.device_count()
    print(f"检测到 {gpu_cnt} 个 CUDA 设备（PyTorch）")
    for i in range(gpu_cnt):
        try:
            name = torch.cuda.get_device_name(i)
            cc = torch.cuda.get_device_capability(i)
            total_mem = torch.cuda.get_device_properties(i).total_memory
            print(f"[Torch] 设备 {i}: {name}")
            print(f"  计算能力: {cc[0]}.{cc[1]}")
            print(f"  总显存: {human_readable_bytes(total_mem)}")
        except Exception as e:
            print(f"[Torch] 设备 {i} 信息查询失败: {e}")
        print("-" * 60)

if __name__ == "__main__":
    main()
