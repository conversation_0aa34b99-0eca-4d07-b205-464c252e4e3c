#!/usr/bin/env python3
"""
Obsidian Wikilink Cleaner Function Library

Function library for cleaning broken wikilinks in Obsidian markdown files.
Processes 'whisper.md' files and files matching the 'W *.md' pattern.
Designed for integration with P.py pipeline system.

Author: Generated for Obsidian maintenance
"""

import re
import os
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Tuple, Set, Dict, Optional
import shutil

# Global variables for integration with P.py
_shared_logger = None
_cleaning_stats = {
    'files_processed': 0,
    'broken_links_found': 0,
    'broken_links_removed': 0,
    'files_modified': 0,
    'last_run': None,
    'errors': 0
}

def setup_wikilink_cleaner_logging(existing_logger: logging.Logger) -> None:
    """Configure logging to use P.py's logger instance."""
    global _shared_logger
    _shared_logger = existing_logger

def get_cleaning_stats() -> Dict[str, any]:
    """Return cleaning statistics for status reporting."""
    return _cleaning_stats.copy()

def clean_dead_links(target_dir: str, backup_dir: str = None, create_backup: bool = True, dry_run: bool = False, max_files: int = 50, file_lock_functions: Dict = None) -> Dict[str, any]:
    """
    Main cleaning function for integration with P.py.

    Args:
        target_dir: Directory containing markdown files to clean
        backup_dir: Directory for backup files (defaults to target_dir/Archive/link_backup)
        create_backup: Whether to create backup files
        dry_run: Whether to perform a dry run without making changes
        max_files: Maximum number of files to process per run

    Returns:
        Dictionary with cleaning statistics
    """
    global _cleaning_stats, _shared_logger

    # Reset stats for this run
    run_stats = {
        'files_processed': 0,
        'broken_links_found': 0,
        'broken_links_removed': 0,
        'files_modified': 0,
        'errors': 0
    }

    try:
        if _shared_logger:
            _shared_logger.info("WikilinkCleaner: Starting dead link cleaning cycle")

        cleaner = WikilinkCleaner(target_dir, backup_dir, create_backup, dry_run, max_files, file_lock_functions)
        success = cleaner.run_cleaning()

        # Update global stats
        run_stats = cleaner.get_stats()
        _cleaning_stats['files_processed'] += run_stats['files_processed']
        _cleaning_stats['broken_links_found'] += run_stats['broken_links_found']
        _cleaning_stats['broken_links_removed'] += run_stats['broken_links_removed']
        _cleaning_stats['files_modified'] += run_stats['files_modified']
        _cleaning_stats['errors'] += run_stats['errors']
        _cleaning_stats['last_run'] = datetime.now()

        if _shared_logger:
            mode = "DRY RUN" if dry_run else "LIVE"
            _shared_logger.info(f"WikilinkCleaner: Completed {mode} - Files: {run_stats['files_processed']}, "
                              f"Links removed: {run_stats['broken_links_removed']}, "
                              f"Files modified: {run_stats['files_modified']}")

        return run_stats

    except Exception as e:
        run_stats['errors'] += 1
        _cleaning_stats['errors'] += 1
        if _shared_logger:
            _shared_logger.error(f"WikilinkCleaner: Error during cleaning cycle: {str(e)}")
        return run_stats


class WikilinkCleaner:
    """Internal class for cleaning broken wikilinks in Obsidian markdown files (whisper.md and W *.md files)."""

    def __init__(self, target_dir: str, backup_dir: str = None, create_backup: bool = True, dry_run: bool = False, max_files: int = 50, file_lock_functions: Dict = None):
        self.target_dir = Path(target_dir)
        self.backup_dir = Path(backup_dir) if backup_dir else Path(target_dir).parent / "Archive" / "link_backup"
        self.backup_enabled = create_backup
        self.dry_run = dry_run
        self.max_files = max_files
        self.logger = _shared_logger
        self.file_lock_functions = file_lock_functions or {}
        self.stats = {
            'files_processed': 0,
            'broken_links_found': 0,
            'broken_links_removed': 0,
            'files_modified': 0,
            'errors': 0
        }

        # Regex pattern to match wikilinks: [[filename]] or [[filename.md]]
        self.wikilink_pattern = re.compile(r'\[\[([^\]]+)\]\]')

        # Ensure backup directory exists
        if self.backup_enabled:
            self.backup_dir.mkdir(parents=True, exist_ok=True)

    def get_stats(self) -> Dict[str, any]:
        """Return current cleaning statistics."""
        return self.stats.copy()
        
    def find_target_files(self) -> List[Path]:
        """Find all target markdown files in the directory (whisper.md and W *.md files)."""
        target_files = []

        if not self.target_dir.exists():
            if self.logger:
                self.logger.error(f"WikilinkCleaner: Target directory does not exist: {self.target_dir}")
            return target_files

        # Find whisper.md files (case insensitive)
        for file_path in self.target_dir.glob("whisper.md"):
            if file_path.is_file():
                target_files.append(file_path)
                if self.logger:
                    self.logger.debug(f"WikilinkCleaner: Found whisper.md file: {file_path.name}")

        # Also check for Whisper.md (capital W)
        for file_path in self.target_dir.glob("Whisper.md"):
            if file_path.is_file():
                target_files.append(file_path)
                if self.logger:
                    self.logger.debug(f"WikilinkCleaner: Found Whisper.md file: {file_path.name}")

        # Find W *.md files (files starting with "W " followed by any characters)
        for file_path in self.target_dir.glob("W *.md"):
            if file_path.is_file():
                target_files.append(file_path)
                if self.logger:
                    self.logger.debug(f"WikilinkCleaner: Found W pattern file: {file_path.name}")

        # Limit files processed per run
        if len(target_files) > self.max_files:
            target_files = target_files[:self.max_files]
            if self.logger:
                self.logger.info(f"WikilinkCleaner: Limited to {self.max_files} files per cleaning cycle")

        if self.logger:
            self.logger.debug(f"WikilinkCleaner: Found {len(target_files)} target markdown files to process")
        return target_files
        
    def get_existing_files(self, directory: Path) -> Set[str]:
        """Get set of all existing .md files in the directory (without extension)."""
        existing_files = set()
        
        for file_path in directory.glob("*.md"):
            if file_path.is_file():
                # Store both with and without .md extension for flexible matching
                filename_without_ext = file_path.stem
                filename_with_ext = file_path.name
                existing_files.add(filename_without_ext)
                existing_files.add(filename_with_ext)
                
        return existing_files
        
    def extract_wikilinks(self, content: str) -> List[Tuple[str, str]]:
        """Extract all wikilinks from content. Returns list of (full_match, filename) tuples."""
        wikilinks = []
        
        for match in self.wikilink_pattern.finditer(content):
            full_match = match.group(0)  # [[filename]]
            filename = match.group(1).strip()  # filename
            wikilinks.append((full_match, filename))
            
        return wikilinks
        
    def is_link_broken(self, filename: str, existing_files: Set[str]) -> bool:
        """Check if a wikilink is broken (target file doesn't exist)."""
        # Check both with and without .md extension
        if filename in existing_files:
            return False
            
        # If filename doesn't end with .md, also check with .md extension
        if not filename.endswith('.md'):
            if f"{filename}.md" in existing_files:
                return False
                
        return True


    def create_backup(self, file_path: Path) -> bool:
        """Create a backup of the file before modification in dedicated backup directory."""
        if not self.backup_enabled:
            return True

        try:
            # Create backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
            backup_path = self.backup_dir / backup_name

            # Copy file to backup location
            shutil.copy2(file_path, backup_path)
            if self.logger:
                self.logger.debug(f"WikilinkCleaner: Created backup: {backup_path}")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"WikilinkCleaner: Failed to create backup for {file_path}: {e}")
            self.stats['errors'] += 1
            return False
            
    def process_file(self, file_path: Path) -> bool:
        """Process a single markdown file to remove broken wikilinks."""
        # Try to acquire file lock if locking functions are available
        file_path_str = str(file_path)
        lock_acquired = False

        if self.file_lock_functions and 'acquire' in self.file_lock_functions:
            try:
                lock_acquired = self.file_lock_functions['acquire'](file_path_str)
                if not lock_acquired:
                    if self.logger:
                        self.logger.debug(f"WikilinkCleaner: File {file_path.name} is locked, skipping")
                    return True  # Skip locked files, don't count as error
            except Exception as e:
                if self.logger:
                    self.logger.debug(f"WikilinkCleaner: Error acquiring lock for {file_path.name}: {e}")
                return True  # Skip on lock error

        try:
            if self.logger:
                self.logger.debug(f"WikilinkCleaner: Processing file: {file_path}")

            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # Get existing files in the same directory
            existing_files = self.get_existing_files(file_path.parent)

            # Extract wikilinks
            wikilinks = self.extract_wikilinks(original_content)

            if not wikilinks:
                if self.logger:
                    self.logger.debug(f"WikilinkCleaner: No wikilinks found in {file_path}")
                self.stats['files_processed'] += 1
                return True

            # Process content line by line to handle entire line removal and adjacent empty line cleanup
            lines = original_content.split('\n')
            broken_links_in_file = 0
            removed_line_indices = set()  # Track which lines were removed due to broken wikilinks

            # First pass: identify broken wikilinks and mark lines for removal
            for i, line in enumerate(lines):
                # Extract wikilinks from this line
                line_wikilinks = self.extract_wikilinks(line)
                line_modified = line
                line_had_broken_links = False

                # Check each wikilink in this line
                for full_match, filename in line_wikilinks:
                    if self.is_link_broken(filename, existing_files):
                        if self.logger:
                            self.logger.debug(f"WikilinkCleaner: Found broken wikilink: {full_match} -> {filename}")
                        self.stats['broken_links_found'] += 1
                        broken_links_in_file += 1
                        line_had_broken_links = True

                        # Remove the broken wikilink from the line (for both dry run and live mode)
                        line_modified = line_modified.replace(full_match, "")

                        if not self.dry_run:
                            self.stats['broken_links_removed'] += 1
                            if self.logger:
                                self.logger.debug(f"WikilinkCleaner: Removed broken wikilink: {full_match}")

                # Mark line for removal if it becomes empty after removing broken wikilinks
                if line_had_broken_links:
                    if line_modified.strip() == "":
                        removed_line_indices.add(i)
                        if self.logger:
                            self.logger.debug(f"WikilinkCleaner: Marked line {i+1} for removal (contained only broken wikilinks)")
                    else:
                        if self.logger:
                            self.logger.debug(f"WikilinkCleaner: Line {i+1} has other content besides broken wikilinks, keeping it")

                # Update the line content for lines that had broken wikilinks but still have content
                if line_had_broken_links and not self.dry_run and line_modified.strip() != "":
                    lines[i] = line_modified

            # Second pass: identify adjacent empty lines to remove
            adjacent_empty_lines_to_remove = set()

            # More aggressive approach: remove empty lines adjacent to removed broken wikilinks
            # unless they are clearly needed for spacing between active content
            for i, line in enumerate(lines):
                # Skip if this line is already marked for removal
                if i in removed_line_indices:
                    continue

                # Check if this is an empty line
                if line.strip() == "":
                    # Check if this empty line is adjacent to any removed broken wikilink line
                    is_adjacent_to_removed = False
                    adjacent_reason = ""

                    # Check if line before is a removed broken wikilink
                    if i > 0 and (i - 1) in removed_line_indices:
                        is_adjacent_to_removed = True
                        adjacent_reason = f"after removed line {i}"

                    # Check if line after is a removed broken wikilink
                    if i < len(lines) - 1 and (i + 1) in removed_line_indices:
                        is_adjacent_to_removed = True
                        if adjacent_reason:
                            adjacent_reason = f"between removed lines {i} and {i + 2}"
                        else:
                            adjacent_reason = f"before removed line {i + 2}"

                    # If adjacent to removed lines, be more aggressive about removal
                    if is_adjacent_to_removed:
                        should_preserve = False

                        # Only preserve if BOTH adjacent lines (before and after) have active wikilinks
                        # This is much more restrictive than before
                        prev_has_active = False
                        next_has_active = False

                        # Check line before the empty line for active wikilinks
                        if i > 0 and (i - 1) not in removed_line_indices:
                            prev_line_wikilinks = self.extract_wikilinks(lines[i - 1])
                            if prev_line_wikilinks:
                                prev_has_active = any(not self.is_link_broken(filename, existing_files)
                                                    for _, filename in prev_line_wikilinks)

                        # Check line after the empty line for active wikilinks
                        if i < len(lines) - 1 and (i + 1) not in removed_line_indices:
                            next_line_wikilinks = self.extract_wikilinks(lines[i + 1])
                            if next_line_wikilinks:
                                next_has_active = any(not self.is_link_broken(filename, existing_files)
                                                    for _, filename in next_line_wikilinks)

                        # Only preserve if both before AND after have active wikilinks
                        # This ensures we only keep spacing that's truly needed between active content
                        if prev_has_active and next_has_active:
                            should_preserve = True
                            if self.logger:
                                self.logger.debug(f"WikilinkCleaner: Preserving empty line {i + 1} - needed spacing between active wikilinks")

                        # Remove the empty line if not preserved
                        if not should_preserve:
                            adjacent_empty_lines_to_remove.add(i)
                            if self.logger:
                                self.logger.debug(f"WikilinkCleaner: Marked adjacent empty line {i + 1} for removal ({adjacent_reason})")

            # Third pass: build the final content excluding removed lines
            modified_lines = []
            all_removed_indices = removed_line_indices.union(adjacent_empty_lines_to_remove)

            for i, line in enumerate(lines):
                if i not in all_removed_indices:
                    modified_lines.append(line)

            # Write modified content if changes were made
            if broken_links_in_file > 0 and not self.dry_run:
                modified_content = '\n'.join(modified_lines)

                # Create backup first
                if not self.create_backup(file_path):
                    if self.logger:
                        self.logger.error(f"WikilinkCleaner: Skipping file due to backup failure: {file_path}")
                    return False

                # Write modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)

                self.stats['files_modified'] += 1
                empty_lines_removed = len(adjacent_empty_lines_to_remove)
                if self.logger:
                    if empty_lines_removed > 0:
                        self.logger.info(f"WikilinkCleaner: Modified {file_path.name} ({broken_links_in_file} broken links removed, {empty_lines_removed} adjacent empty lines cleaned)")
                    else:
                        self.logger.info(f"WikilinkCleaner: Modified {file_path.name} ({broken_links_in_file} broken links removed)")

            elif broken_links_in_file > 0 and self.dry_run:
                empty_lines_would_remove = len(adjacent_empty_lines_to_remove)
                if self.logger:
                    if empty_lines_would_remove > 0:
                        self.logger.info(f"WikilinkCleaner: DRY RUN - Would remove {broken_links_in_file} broken links and {empty_lines_would_remove} adjacent empty lines from {file_path.name}")
                    else:
                        self.logger.info(f"WikilinkCleaner: DRY RUN - Would remove {broken_links_in_file} broken links from {file_path.name}")

            self.stats['files_processed'] += 1
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"WikilinkCleaner: Error processing file {file_path}: {e}")
            self.stats['errors'] += 1
            return False
        finally:
            # Release file lock if it was acquired
            if lock_acquired and self.file_lock_functions and 'release' in self.file_lock_functions:
                try:
                    self.file_lock_functions['release'](file_path_str)
                    if 'cleanup' in self.file_lock_functions:
                        self.file_lock_functions['cleanup'](file_path_str)
                except Exception as e:
                    if self.logger:
                        self.logger.debug(f"WikilinkCleaner: Error releasing lock for {file_path.name}: {e}")

    def run_cleaning(self) -> bool:
        """Main execution method for integration with P.py."""
        # Find target markdown files
        target_files = self.find_target_files()

        if not target_files:
            if self.logger:
                self.logger.debug("WikilinkCleaner: No target markdown files found to process")
            return True

        # Process each file
        success = True
        for file_path in target_files:
            if not self.process_file(file_path):
                success = False

        return success
