import pycuda.driver as cuda
import pycuda.autoinit

def check_cuda():
    try:
        device_count = cuda.Device.count()
        if device_count == 0:
            print("没有检测到 CUDA 设备。")
        else:
            print(f"检测到 {device_count} 个 CUDA 设备。")
            for i in range(device_count):
                device = cuda.Device(i)
                print(f"设备 {i}: {device.name()}")
                print(f"计算能力: {device.compute_capability()}")
                print(f"内存总量: {device.total_memory() / (1024 ** 2)} MB")
                print("-" * 50)
    except cuda.Error as e:
        print(f"CUDA 检测失败: {e}")

check_cuda()


