# 基于轻量的 CUDA runtime 镜像
FROM nvidia/cuda:12.8.0-runtime-ubuntu20.04

# 安装 git、pip，然后清理 apt 缓存
RUN apt-get update \
 && apt-get install -y git python3-pip \
 && rm -rf /var/lib/apt/lists/*

# 拷贝并安装你项目的所有 Python 依赖
COPY requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# 最后安装 NVML Python 绑定
RUN pip3 install --no-cache-dir nvidia-ml-py3

# 安装 PyTorch 支持 CUDA 12.8 的 nightly 版本（含 sm_120 架构）
# 不使用 --no-cache-dir，利用外部挂载的 pip 缓存
RUN pip3 install --upgrade pip && \
    pip3 install --pre --upgrade \
      torch torchvision torchaudio \
      --extra-index-url https://download.pytorch.org/whl/nightly/cu120

# 将 pip 缓存指向一个可挂载的目录（后面做 volume）
ENV PIP_CACHE_DIR=/root/.cache/pip
