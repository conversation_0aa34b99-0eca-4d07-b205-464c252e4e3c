{
  "name": "Python 3 with CUDA",
  "image": "nvidia/cuda:12.8.0-base-ubuntu20.04",  // 使用最基础的 CUDA 镜像
  "postCreateCommand": "apt update && apt install -y python3.10 python3.10-dev python3.10-distutils python3-pip g++ && pip3 install pycuda && pip3 install -r /workspaces/Test/requirements.txt",  // 安装 Python 3.10, pip, g++ 和 pycuda
  "runArgs": [
    "--runtime=nvidia",  // 确保容器使用 NVIDIA 运行时
    "--gpus", "all"      // 让容器可以访问所有 GPU
  ],
  "remoteUser": "root"
}
