{
  "name": "CUDA 12.8 + PyTorch cu120",
  "build": {
    "dockerfile": "Dockerfile",
    "context": ".."
  },
  "runArgs": [
    "--runtime=nvidia",
    "--gpus",
    "all"
  ],

  // 只挂载 pip 缓存，不动你的 /workspaces/Test
  "mounts": [
    "source=my_pip_cache,target=/root/.cache/pip,type=volume",
    // 新增：把 VS Code 扩展目录挂到一个持久卷
    "source=vscode-extensions,target=/root/.vscode-server/extensions,type=volume",
    // Mount Windows directories to access original file locations
    "source=C:\\Users\\<USER>\\Desktop,target=/mnt/desktop,type=bind"
    // Note: iCloudDrive mount temporarily disabled - may need path verification
    // "source=C:\\Users\\<USER>\\iCloudDrive\\iCloud~md~obsidian,target=/mnt/obsidian,type=bind"
  ],

  // 新增：声明要自动安装的 VS Code 插件
  "customizations": {
    "vscode": {
      "extensions": [
        "augment.vscode-augment"
      ]
    }
  },

  "remoteUser": "root",
  // 全部安装都在 Dockerfile 里做，postCreateCommand 可留空或删掉
  "postCreateCommand": ""
}
