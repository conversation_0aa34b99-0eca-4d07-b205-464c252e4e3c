import os
import logging
import openai
import shutil
from datetime import datetime
from watchdog.events import FileSystemEventHandler

from utils import (
    get_model_display_name, 
    process_text_with_openai, 
    find_most_recent_md_by_prefix
)

class ExtractHandler(FileSystemEventHandler):
    def __init__(self, config, queue):
        self.config = config
        self.queue = queue
        self.processed_files = set()

    def on_created(self, event):
        if event.is_directory:
            return
        try:
            file_path = event.src_path
            if (os.path.dirname(file_path) == self.config['WATCH_FOLDER'] and
                file_path.lower().endswith('_p.txt')):

                filename = os.path.basename(file_path)

                try:
                    os.path.basename(file_path).encode('ascii')
                except UnicodeEncodeError:
                    logging.debug(f"Processing non-ASCII pretext filename: {filename}")

                if file_path not in self.processed_files:
                    self.queue.put(file_path)
                    self.processed_files.add(file_path)
                else:
                    logging.debug(f"File already processed by extract handler: {filename}")

        except Exception as e:
            logging.error(f"Error in ExtractHandler.on_created: {str(e)}")

    def process_extract(self, file_path, get_next_available_filename):
        try:
            filename = os.path.basename(file_path)

            if not filename.lower().endswith('_p.txt'):
                logging.error(f"Extract process received non-pretext file: {filename}")
                return

            if filename.lower().endswith('_p.txt'):
                base_name = filename[:-6]
            else:
                base_name = os.path.splitext(filename)[0]
                if base_name.endswith('_p'):
                    base_name = base_name[:-2]

            # Read file to get character count
            content = None
            encoding_used = None
            for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        encoding_used = encoding
                        break
                except UnicodeDecodeError:
                    continue
            if content is None:
                raise ValueError(f"Unable to read file: {file_path}")

            char_count = len(content)
            logging.debug(f"File read successfully using {encoding_used} encoding, content length: {len(content):,} characters")

            client = openai.OpenAI(api_key=self.config['OPENAI_API_KEY'])
            content_with_filename = f"\u300a{base_name}\u300b\n{content}"

            primary_model_name = get_model_display_name(self.config.get('GPT_MODEL_EXTRACT_1'))
            secondary_model_name = get_model_display_name(self.config.get('GPT_MODEL_EXTRACT_2'))

            logging.debug(f"Primary model: {self.config.get('GPT_MODEL_EXTRACT_1')} ({primary_model_name})")
            logging.debug(f"Secondary model: {self.config.get('GPT_MODEL_EXTRACT_2')} ({secondary_model_name})")

            logging.info(f"Extract processing: Starting first extraction with {self.config['GPT_MODEL_EXTRACT_1']} for {filename}")
            try:
                extract_result_1 = process_text_with_openai(client, self.config['GPT_MODEL_EXTRACT_1'], self.config['EXTRACT_PROMPT'], content_with_filename, self.config, file_path=file_path)
            except Exception as e:
                logging.error(f"Extract API call 1 failed for {filename}: {str(e)}")
                raise
            if not extract_result_1:
                raise ValueError(f"Empty response from {primary_model_name} (first extraction)")

            os.makedirs(self.config['EXTRACT_FOLDER'], exist_ok=True)
            extract_target_path_1 = get_next_available_filename(self.config['EXTRACT_FOLDER'], base_name, '_e')
            with open(extract_target_path_1, 'w', encoding='utf-8') as f:
                f.write(extract_result_1)

            extract_filename_1 = os.path.basename(extract_target_path_1)
            extract_char_count_1 = len(extract_result_1)
            logging.info(f"Extract processing: Created first extract file {extract_filename_1} (characters: {extract_char_count_1:,})")

            logging.info(f"Extract processing: Starting second extraction with {self.config['GPT_MODEL_EXTRACT_2']} for {filename}")
            try:
                extract_result_2 = process_text_with_openai(client, self.config['GPT_MODEL_EXTRACT_2'], self.config['EXTRACT_PROMPT'], content_with_filename, self.config, file_path=file_path)
            except Exception as e:
                logging.error(f"Extract API call 2 failed for {filename}: {str(e)}")
                raise
            if not extract_result_2:
                raise ValueError(f"Empty response from {secondary_model_name} (second extraction)")

            extract_target_path_2 = get_next_available_filename(self.config['EXTRACT_FOLDER'], base_name, '_e')
            with open(extract_target_path_2, 'w', encoding='utf-8') as f:
                f.write(extract_result_2)

            extract_filename_2 = os.path.basename(extract_target_path_2)
            extract_char_count_2 = len(extract_result_2)
            logging.info(f"Extract processing: Created second extract file {extract_filename_2} (characters: {extract_char_count_2:,})")

            md_base_name = base_name
            os.makedirs(self.config['OBSIDIAN_SYNC_FOLDER'], exist_ok=True)

            logging.debug(f"Extract processing: Looking for existing markdown file with prefix: {md_base_name}")
            md_path, found_date = find_most_recent_md_by_prefix(self.config['OBSIDIAN_SYNC_FOLDER'], md_base_name)

            if md_path is None:
                # Instead of error, treat as new pretext: create new markdown file with datecode and link to Whisper.md
                datecode = datetime.now().strftime('%y%m%d')
                md_filename = f"{md_base_name}_{datecode}.md"
                md_path = os.path.join(self.config['OBSIDIAN_SYNC_FOLDER'], md_filename)
                # Include the original _p.txt content as well
                with open(file_path, 'r', encoding=encoding_used) as f:
                    original_p_content = f.read()
                merged_content = f"# {primary_model_name} Extract\n\n{extract_result_1}\n\n---\n\n# {secondary_model_name} Extract\n\n{extract_result_2}\n\n---\n\n# Original Pretext\n\n{original_p_content}"
                with open(md_path, 'w', encoding='utf-8') as f:
                    f.write(merged_content)
                whisper_md_path = os.path.join(self.config['OBSIDIAN_SYNC_FOLDER'], 'Whisper.md')
                link_code = f"[[{md_base_name}_{datecode}]]\n"
                try:
                    if os.path.exists(whisper_md_path):
                        with open(whisper_md_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        if lines:
                            insert_index = None
                            for i, line in enumerate(lines):
                                if line.strip() == "---":
                                    insert_index = i + 1
                                    break
                            if insert_index is not None:
                                if insert_index < len(lines) and lines[insert_index].strip() == "":
                                    lines.insert(insert_index + 1, link_code)
                                else:
                                    lines.insert(insert_index, "\n")
                                    lines.insert(insert_index + 1, link_code)
                            else:
                                lines.insert(1, link_code)
                        else:
                            lines = [link_code]
                        with open(whisper_md_path, 'w', encoding='utf-8') as f:
                            f.writelines(lines)
                    else:
                        with open(whisper_md_path, 'w', encoding='utf-8') as f:
                            f.write(link_code)
                except Exception as e:
                    logging.error(f"Error updating Whisper.md: {str(e)}")
                logging.info(f"No existing markdown file found for {md_base_name}. Created new markdown and linked in Whisper.md.")
                pretext_md_content = f"# Original Pretext\n\n{original_p_content}"
            else:
                md_filename = os.path.basename(md_path)
                if os.path.exists(md_path):
                    with open(md_path, 'r', encoding='utf-8') as f:
                        pretext_md_content = f.read()
                    logging.debug(f"Read existing markdown content, length: {len(pretext_md_content):,} characters")
                else:
                    pretext_md_content = ''
                    logging.warning(f"Markdown file not found: {md_path}")

            merged_content = f"# {primary_model_name} Extract\n\n{extract_result_1}\n\n---\n\n# {secondary_model_name} Extract\n\n{extract_result_2}\n\n---\n\n{pretext_md_content}"
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(merged_content)

            logging.info(f"Extract processing: Merged dual-extraction results into {os.path.basename(md_path)} (final size: {len(merged_content):,} characters)")

            os.makedirs(self.config['PRETEXT_FOLDER'], exist_ok=True)
            pretext_target_path = os.path.join(self.config['PRETEXT_FOLDER'], filename)
            shutil.move(file_path, pretext_target_path)

        except Exception as e:
            logging.error(f"Error processing file: {str(e)}")
            primary_model_name = get_model_display_name(self.config.get('GPT_MODEL_EXTRACT_1'))
            secondary_model_name = get_model_display_name(self.config.get('GPT_MODEL_EXTRACT_2'))
            if 'extract_result_1' in locals() and 'extract_target_path_1' in locals():
                with open(extract_target_path_1 + ".error.txt", 'w', encoding='utf-8') as f:
                    f.write(f"Error: {str(e)}\nFirst extraction ({primary_model_name}) response:\n{extract_result_1}")
            if 'extract_result_2' in locals() and 'extract_target_path_2' in locals():
                with open(extract_target_path_2 + ".error.txt", 'w', encoding='utf-8') as f:
                    f.write(f"Error: {str(e)}\nSecond extraction ({secondary_model_name}) response:\n{extract_result_2}")
            raise

class PremiumExtractHandler(FileSystemEventHandler):
    def __init__(self, config, queue):
        self.config = config
        self.queue = queue
        self.processed_files = set()

    def on_created(self, event):
        if event.is_directory:
            return
        try:
            file_path = event.src_path
            if (os.path.dirname(file_path) == r'C:\Users\<USER>\Desktop' and
                file_path.lower().endswith('_p.txt')):

                filename = os.path.basename(file_path)

                try:
                    os.path.basename(file_path).encode('ascii')
                except UnicodeEncodeError:
                    logging.debug(f"Processing non-ASCII premium filename: {filename}")

                if file_path not in self.processed_files:
                    self.queue.put(file_path)
                    self.processed_files.add(file_path)
                    logging.info(f"Queue: Added file to premium extract queue: {filename}")
                else:
                    logging.debug(f"File already processed by premium extract handler: {filename}")

        except Exception as e:
            logging.error(f"Error in PremiumExtractHandler.on_created: {str(e)}")

    def process_premium_extract(self, file_path, get_next_available_filename):
        try:
            filename = os.path.basename(file_path)

            if not filename.lower().endswith('_p.txt'):
                logging.error(f"Premium extract process received non-pretext file: {filename}")
                return

            if filename.lower().endswith('_p.txt'):
                base_name = filename[:-6]
            else:
                base_name = os.path.splitext(filename)[0]
                if base_name.endswith('_p'):
                    base_name = base_name[:-2]

            # Read file to get character count
            content = None
            encoding_used = None
            for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        encoding_used = encoding
                        break
                except UnicodeDecodeError:
                    continue
            if content is None:
                raise ValueError(f"Unable to read file: {file_path}")

            char_count = len(content)
            logging.debug(f"File read successfully using {encoding_used} encoding, content length: {len(content):,} characters")

            client = openai.OpenAI(api_key=self.config['OPENAI_API_KEY'])
            content_with_filename = f"\u300a{base_name}\u300b\n{content}"
            premium_model_name = get_model_display_name(self.config.get('GPT_MODEL_EXTRACT_3'))
            logging.debug(f"Premium model: {self.config.get('GPT_MODEL_EXTRACT_3')} ({premium_model_name})")
            logging.info(f"Premium extract processing: Starting extraction with {self.config['GPT_MODEL_EXTRACT_3']} for {filename}")
            try:
                extract_result = process_text_with_openai(client, self.config['GPT_MODEL_EXTRACT_3'], self.config['EXTRACT_PROMPT'], content_with_filename, self.config, file_path=file_path)
            except Exception as e:
                logging.error(f"Premium extract API call failed for {filename}: {str(e)}")
                raise
            if not extract_result:
                raise ValueError(f"Empty response from {premium_model_name} (premium extraction)")

            os.makedirs(self.config['EXTRACT_FOLDER'], exist_ok=True)
            extract_target_path = get_next_available_filename(self.config['EXTRACT_FOLDER'], base_name, '_e')
            with open(extract_target_path, 'w', encoding='utf-8') as f:
                f.write(extract_result)

            extract_filename = os.path.basename(extract_target_path)
            extract_char_count = len(extract_result)
            logging.info(f"Premium extract processing: Created extract file {extract_filename} (characters: {extract_char_count:,})")

            md_base_name = base_name
            os.makedirs(self.config['OBSIDIAN_SYNC_FOLDER'], exist_ok=True)

            logging.debug(f"Premium extract processing: Looking for existing markdown file with prefix: {md_base_name}")
            md_path, found_date = find_most_recent_md_by_prefix(self.config['OBSIDIAN_SYNC_FOLDER'], md_base_name)

            if md_path is None:
                # Instead of error, treat as new pretext: create new markdown file with datecode and link to Whisper.md
                datecode = datetime.now().strftime('%y%m%d')
                md_filename = f"{md_base_name}_{datecode}.md"
                md_path = os.path.join(self.config['OBSIDIAN_SYNC_FOLDER'], md_filename)
                # Include the original _p.txt content as well
                with open(file_path, 'r', encoding=encoding_used) as f:
                    original_p_content = f.read()
                merged_content = f"# {premium_model_name} Premium Extract\n\n{extract_result}\n\n---\n\n# Original Pretext\n\n{original_p_content}"
                with open(md_path, 'w', encoding='utf-8') as f:
                    f.write(merged_content)
                whisper_md_path = os.path.join(self.config['OBSIDIAN_SYNC_FOLDER'], 'Whisper.md')
                link_code = f"[[{md_base_name}_{datecode}]]\n"
                try:
                    if os.path.exists(whisper_md_path):
                        with open(whisper_md_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        if lines:
                            insert_index = None
                            for i, line in enumerate(lines):
                                if line.strip() == "---":
                                    insert_index = i + 1
                                    break
                            if insert_index is not None:
                                if insert_index < len(lines) and lines[insert_index].strip() == "":
                                    lines.insert(insert_index + 1, link_code)
                                else:
                                    lines.insert(insert_index, "\n")
                                    lines.insert(insert_index + 1, link_code)
                            else:
                                lines.insert(1, link_code)
                        else:
                            lines = [link_code]
                        with open(whisper_md_path, 'w', encoding='utf-8') as f:
                            f.writelines(lines)
                    else:
                        with open(whisper_md_path, 'w', encoding='utf-8') as f:
                            f.write(link_code)
                except Exception as e:
                    logging.error(f"Error updating Whisper.md: {str(e)}")
                logging.info(f"No existing markdown file found for {md_base_name}. Created new markdown and linked in Whisper.md.")
                pretext_md_content = f"# Original Pretext\n\n{original_p_content}"
            else:
                md_filename = os.path.basename(md_path)
                if os.path.exists(md_path):
                    with open(md_path, 'r', encoding='utf-8') as f:
                        pretext_md_content = f.read()
                    logging.debug(f"Read existing markdown content, length: {len(pretext_md_content):,} characters")
                else:
                    pretext_md_content = ''
                    logging.warning(f"Markdown file not found: {md_path}")

            merged_content = f"# {premium_model_name} Premium Extract\n\n{extract_result}\n\n---\n\n{pretext_md_content}"
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(merged_content)

            logging.info(f"Premium extract processing: Merged premium extraction result into {os.path.basename(md_path)} (final size: {len(merged_content):,} characters)")

            os.makedirs(self.config['PRETEXT_FOLDER'], exist_ok=True)
            pretext_target_path = os.path.join(self.config['PRETEXT_FOLDER'], filename)
            shutil.move(file_path, pretext_target_path)

        except Exception as e:
            logging.error(f"Error processing premium extract file: {str(e)}")
            premium_model_name = get_model_display_name(self.config.get('GPT_MODEL_EXTRACT_3'))
            if 'extract_result' in locals() and 'extract_target_path' in locals():
                with open(extract_target_path + ".error.txt", 'w', encoding='utf-8') as f:
                    f.write(f"Error: {str(e)}\nPremium extraction ({premium_model_name}) response:\n{extract_result}")
            raise
